'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { tutorialSteps } from '@/lib/tutorial-steps'

export interface TutorialStep {
  id: string
  title: string
  content: string
  target?: string // CSS selector for element to highlight
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  action?: 'click' | 'hover' | 'scroll' | 'none'
  page?: string // Page where this step should appear
  category: 'welcome' | 'writing' | 'audio' | 'profile' | 'discovery' | 'advanced'
  order: number
  optional?: boolean
}

export interface TutorialState {
  isActive: boolean
  isVisible: boolean
  currentStep: number
  currentCategory: string | null
  completedSteps: string[]
  completedCategories: string[]
  hasSeenWelcome: boolean
  userPreferences: {
    showTutorial: boolean
    autoStart: boolean
    showHints: boolean
  }
}

interface TutorialContextType {
  state: TutorialState
  steps: TutorialStep[]
  currentStepData: TutorialStep | null
  
  // Tutorial controls
  startTutorial: (category?: string) => void
  stopTutorial: () => void
  nextStep: () => void
  previousStep: () => void
  skipStep: () => void
  jumpToStep: (stepId: string) => void
  
  // Category management
  startCategory: (category: string) => void
  completeCategory: (category: string) => void
  
  // Visibility controls
  showTutorial: () => void
  hideTutorial: () => void
  toggleTutorial: () => void
  
  // Preferences
  updatePreferences: (preferences: Partial<TutorialState['userPreferences']>) => void
  
  // Progress tracking
  markStepComplete: (stepId: string) => void
  isStepComplete: (stepId: string) => boolean
  isCategoryComplete: (category: string) => boolean
  getProgress: () => { completed: number; total: number; percentage: number }
  
  // Utility
  reset: () => void
  canShowOnPage: (pathname: string) => boolean
}

const TutorialContext = createContext<TutorialContextType | undefined>(undefined)

const STORAGE_KEY = 'onlydiary_tutorial_state'

const defaultState: TutorialState = {
  isActive: false,
  isVisible: true,
  currentStep: 0,
  currentCategory: null,
  completedSteps: [],
  completedCategories: [],
  hasSeenWelcome: false,
  userPreferences: {
    showTutorial: true,
    autoStart: false,
    showHints: true
  }
}

// Tutorial steps are imported from external file for better organization

export function TutorialProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<TutorialState>(defaultState)

  // Load state from localStorage on mount
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') return

    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const parsedState = JSON.parse(saved)
        setState(prev => ({ ...prev, ...parsedState }))
      }
    } catch (error) {
      console.warn('Failed to load tutorial state:', error)
    }
  }, [])

  // Save state to localStorage whenever it changes
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') return

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
    } catch (error) {
      console.warn('Failed to save tutorial state:', error)
    }
  }, [state])

  const getCurrentStepData = useCallback((): TutorialStep | null => {
    if (!state.currentCategory) return null
    
    const categorySteps = tutorialSteps.filter(step => step.category === state.currentCategory)
    return categorySteps[state.currentStep] || null
  }, [state.currentStep, state.currentCategory])

  const startTutorial = useCallback((category = 'welcome') => {
    setState(prev => ({
      ...prev,
      isActive: true,
      isVisible: true,
      currentCategory: category,
      currentStep: 0
    }))
  }, [])

  const stopTutorial = useCallback(() => {
    setState(prev => ({
      ...prev,
      isActive: false,
      currentCategory: null,
      currentStep: 0
    }))
  }, [])

  const nextStep = useCallback(() => {
    const currentStepData = getCurrentStepData()
    if (!currentStepData) return

    const categorySteps = tutorialSteps.filter(step => step.category === state.currentCategory)
    
    if (state.currentStep < categorySteps.length - 1) {
      setState(prev => ({
        ...prev,
        currentStep: prev.currentStep + 1
      }))
    } else {
      // Category completed
      completeCategory(state.currentCategory!)
    }
  }, [state.currentStep, state.currentCategory, getCurrentStepData])

  const previousStep = useCallback(() => {
    if (state.currentStep > 0) {
      setState(prev => ({
        ...prev,
        currentStep: prev.currentStep - 1
      }))
    }
  }, [state.currentStep])

  const skipStep = useCallback(() => {
    nextStep()
  }, [nextStep])

  const jumpToStep = useCallback((stepId: string) => {
    const step = tutorialSteps.find(s => s.id === stepId)
    if (!step) return

    const categorySteps = tutorialSteps.filter(s => s.category === step.category)
    const stepIndex = categorySteps.findIndex(s => s.id === stepId)
    
    if (stepIndex !== -1) {
      setState(prev => ({
        ...prev,
        currentCategory: step.category,
        currentStep: stepIndex,
        isActive: true,
        isVisible: true
      }))
    }
  }, [])

  const startCategory = useCallback((category: string) => {
    setState(prev => ({
      ...prev,
      isActive: true,
      isVisible: true,
      currentCategory: category,
      currentStep: 0
    }))
  }, [])

  const completeCategory = useCallback((category: string) => {
    setState(prev => ({
      ...prev,
      completedCategories: [...new Set([...prev.completedCategories, category])],
      isActive: false,
      currentCategory: null,
      currentStep: 0
    }))
  }, [])

  const showTutorial = useCallback(() => {
    setState(prev => ({ ...prev, isVisible: true }))
  }, [])

  const hideTutorial = useCallback(() => {
    setState(prev => ({ ...prev, isVisible: false }))
  }, [])

  const toggleTutorial = useCallback(() => {
    setState(prev => ({ ...prev, isVisible: !prev.isVisible }))
  }, [])

  const updatePreferences = useCallback((preferences: Partial<TutorialState['userPreferences']>) => {
    setState(prev => ({
      ...prev,
      userPreferences: { ...prev.userPreferences, ...preferences }
    }))
  }, [])

  const markStepComplete = useCallback((stepId: string) => {
    setState(prev => ({
      ...prev,
      completedSteps: [...new Set([...prev.completedSteps, stepId])]
    }))
  }, [])

  const isStepComplete = useCallback((stepId: string) => {
    return state.completedSteps.includes(stepId)
  }, [state.completedSteps])

  const isCategoryComplete = useCallback((category: string) => {
    return state.completedCategories.includes(category)
  }, [state.completedCategories])

  const getProgress = useCallback(() => {
    const total = tutorialSteps.length
    const completed = state.completedSteps.length
    return {
      completed,
      total,
      percentage: total > 0 ? Math.round((completed / total) * 100) : 0
    }
  }, [state.completedSteps])

  const reset = useCallback(() => {
    setState(defaultState)
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      localStorage.removeItem(STORAGE_KEY)
    }
  }, [])

  const canShowOnPage = useCallback((pathname: string) => {
    if (!state.userPreferences.showTutorial) return false
    
    const currentStepData = getCurrentStepData()
    if (!currentStepData) return true
    
    return !currentStepData.page || pathname.includes(currentStepData.page)
  }, [state.userPreferences.showTutorial, getCurrentStepData])

  const value: TutorialContextType = {
    state,
    steps: tutorialSteps,
    currentStepData: getCurrentStepData(),
    
    startTutorial,
    stopTutorial,
    nextStep,
    previousStep,
    skipStep,
    jumpToStep,
    
    startCategory,
    completeCategory,
    
    showTutorial,
    hideTutorial,
    toggleTutorial,
    
    updatePreferences,
    
    markStepComplete,
    isStepComplete,
    isCategoryComplete,
    getProgress,
    
    reset,
    canShowOnPage
  }

  return (
    <TutorialContext.Provider value={value}>
      {children}
    </TutorialContext.Provider>
  )
}

export function useTutorial() {
  const context = useContext(TutorialContext)
  if (context === undefined) {
    throw new Error('useTutorial must be used within a TutorialProvider')
  }
  return context
}
