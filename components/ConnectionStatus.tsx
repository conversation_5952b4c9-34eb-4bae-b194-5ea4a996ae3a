'use client'

import { useState, useEffect } from 'react'

interface ConnectionStatusProps {
  className?: string
}

export function ConnectionStatus({ className = '' }: ConnectionStatusProps) {
  const [isOnline, setIsOnline] = useState(true)
  const [showStatus, setShowStatus] = useState(false)

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined' || typeof navigator === 'undefined') return

    // Initial status
    setIsOnline(navigator.onLine)

    const handleOnline = () => {
      setIsOnline(true)
      setShowStatus(true)
      // Hide success message after 3 seconds
      setTimeout(() => setShowStatus(false), 3000)
    }

    const handleOffline = () => {
      setIsOnline(false)
      setShowStatus(true)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (!showStatus) return null

  return (
    <div className={`fixed top-4 right-4 z-50 transition-all duration-300 max-w-xs ${className}`}>
      <div className={`px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm border ${
        isOnline 
          ? 'bg-green-50/90 border-green-200 text-green-800' 
          : 'bg-red-50/90 border-red-200 text-red-800'
      }`}>
        <div className="flex items-center gap-2 text-sm font-medium">
          <div className={`w-2 h-2 rounded-full ${
            isOnline ? 'bg-green-500' : 'bg-red-500'
          }`} />
          {isOnline ? '🌐 Connection restored' : '📵 No internet connection'}
        </div>
      </div>
    </div>
  )
}

// Hook for components to check connection status
export function useConnectionStatus() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined' || typeof navigator === 'undefined') return

    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return isOnline
}
