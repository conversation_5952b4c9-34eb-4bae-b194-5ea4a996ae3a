'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { subscribeToPush, isPushSupported, isSubscribedToPush } from '@/lib/notifications/push'

export function PushNotificationPrompt() {
  const [showPrompt, setShowPrompt] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [user, setUser] = useState<{ id: string } | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const checkUser = async () => {
      // Check if we're in the browser
      if (typeof window === 'undefined' || typeof Notification === 'undefined') return

      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)

      if (user && isPushSupported()) {
        // Check browser notification permission first
        const permission = Notification.permission
        const hasBeenPrompted = localStorage.getItem('push-notification-prompted')
        const isSubscribed = await isSubscribedToPush()

        // Only show prompt if permission is default (not granted/denied) and user hasn't been prompted
        if (permission === 'default' && !hasBeenPrompted && !isSubscribed) {
          // Show prompt after a short delay for better UX
          setTimeout(() => setShowPrompt(true), 2000)
        }
      }
    }

    checkUser()
  }, [supabase])

  const handleEnable = async () => {
    if (!user) return

    setIsLoading(true)
    localStorage.setItem('push-notification-prompted', 'true')
    try {
      const success = await subscribeToPush(user.id)
      if (success) {
        setShowPrompt(false)
      } else {
        // A more informative message for the user
        const permission = await Notification.requestPermission();
        if (permission === 'denied') {
          alert('You have blocked notifications. To enable them, please go to your browser settings and allow notifications for this site.');
        } else {
          alert('Failed to enable notifications. Please check your browser settings and try again.');
        }
      }
    } catch (error: unknown) {
      const err = error as { message?: string }
      console.error('Error enabling push notifications:', error)
      if (err.message?.includes('not configured')) {
        alert('Push notifications are not configured yet. Please add VAPID keys to your environment variables.')
      } else {
        alert('Failed to enable notifications. Please check your browser settings and try again.')
      }
    } finally {
      setIsLoading(false)
      setShowPrompt(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    localStorage.setItem('push-notification-prompted', 'true')
  }

  if (!showPrompt || !user) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 max-w-sm bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 z-50 animate-slide-up">
      <div className="flex items-start gap-4">
        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
          <span className="text-white text-xl">🔔</span>
        </div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-gray-800 mb-2">
            Stay Connected!
          </h3>
          <p className="text-sm text-gray-600 mb-4 leading-relaxed">
            OnlyDiary is 50x better with push notifications! Get instant alerts when someone comments on your posts or replies to your comments.
          </p>
          
          <div className="flex gap-2">
            <button
              onClick={handleEnable}
              disabled={isLoading}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 transition-all"
            >
              {isLoading ? 'Enabling...' : 'Enable Notifications'}
            </button>
            
            <button
              onClick={handleDismiss}
              className="px-3 py-2 text-gray-500 hover:text-gray-700 text-sm"
            >
              Not now
            </button>
          </div>
        </div>
        
        <button
          onClick={handleDismiss}
          className="text-gray-400 hover:text-gray-600 text-lg leading-none"
        >
          ×
        </button>
      </div>
    </div>
  )
}

// Add CSS for animation
const styles = `
  @keyframes slide-up {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = styles
  document.head.appendChild(styleSheet)
}
