'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

// Google Analytics
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!gaId || typeof window === 'undefined' || typeof document === 'undefined') return

    // Load Google Analytics
    const script1 = document.createElement('script')
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`
    script1.async = true
    document.head.appendChild(script1)

    const script2 = document.createElement('script')
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        page_title: document.title,
        page_location: window.location.href,
      });
    `
    document.head.appendChild(script2)

    return () => {
      try {
        document.head.removeChild(script1)
        document.head.removeChild(script2)
      } catch (error) {
        // Scripts may have already been removed
      }
    }
  }, [gaId])

  useEffect(() => {
    if (!gaId || typeof window === 'undefined') return

    const url = pathname + searchParams.toString()

    // Track page views
    if ((window as { gtag?: (...args: unknown[]) => void }).gtag) {
      (window as { gtag: (...args: unknown[]) => void }).gtag('config', gaId, {
        page_path: url,
      })
    }
  }, [pathname, searchParams, gaId])

  return null
}

// Performance monitoring
export function PerformanceMonitor() {
  useEffect(() => {
    // Check if we're in the browser and performance APIs are available
    if (typeof window === 'undefined' || typeof PerformanceObserver === 'undefined' || !('performance' in window)) return

    // Core Web Vitals monitoring
    // Monitor Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime)
          // Send to analytics
          if ((window as Window & { gtag?: (...args: unknown[]) => void }).gtag) {
            (window as Window & { gtag?: (...args: unknown[]) => void }).gtag!('event', 'web_vitals', {
              name: 'LCP',
              value: Math.round(entry.startTime),
              event_category: 'Web Vitals',
            })
          }
        }
      }
    })

    observer.observe({ entryTypes: ['largest-contentful-paint'] })

    // Monitor Cumulative Layout Shift (CLS)
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as PerformanceEntry & { hadRecentInput?: boolean }).hadRecentInput) {
          clsValue += (entry as PerformanceEntry & { value: number }).value
        }
      }
    })

    clsObserver.observe({ entryTypes: ['layout-shift'] })

    // Send CLS on page unload
    window.addEventListener('beforeunload', () => {
      if ((window as any).gtag) {
        (window as any).gtag('event', 'web_vitals', {
          name: 'CLS',
          value: Math.round(clsValue * 1000),
          event_category: 'Web Vitals',
        })
      }
    })

    return () => {
      observer.disconnect()
      clsObserver.disconnect()
    }
  }, [])

  return null
}

// Custom event tracking
export function trackEvent(eventName: string, parameters?: Record<string, any>) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', eventName, parameters)
  }
}
