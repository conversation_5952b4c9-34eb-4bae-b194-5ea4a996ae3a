'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Download, X } from 'lucide-react'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined' || typeof navigator === 'undefined') return

    // Check if app is already installed (multiple methods)
    const checkInstallation = () => {
      // Method 1: Check display mode
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches

      // Method 2: Check if launched from home screen (iOS)
      const isIOSStandalone = (window.navigator as any).standalone === true

      // Method 3: Check URL parameters (some browsers add these)
      const hasLaunchParams = window.location.search.includes('utm_source=web_app_manifest')

      // Method 4: Check if previously installed (localStorage backup)
      const wasInstalled = localStorage.getItem('pwa-installed') === 'true'

      if (isStandalone || isIOSStandalone || hasLaunchParams || wasInstalled) {
        setIsInstalled(true)
        localStorage.setItem('pwa-installed', 'true')
        return true
      }
      return false
    }

    if (checkInstallation()) {
      return
    }

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // Show install prompt after a delay (more aggressive on mobile)
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const delay = isMobile ? 2000 : 5000 // 2 seconds on mobile, 5 on desktop

      setTimeout(() => {
        const hasSeenPrompt = localStorage.getItem('pwa-install-prompted')
        const isDismissedPermanently = localStorage.getItem('pwa-install-dismissed-permanently')
        if (!hasSeenPrompt && !isDismissedPermanently) {
          setShowInstallPrompt(true)
        }
      }, delay)
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowInstallPrompt(false)
      setDeferredPrompt(null)
      localStorage.setItem('pwa-installed', 'true')
      localStorage.setItem('pwa-install-prompted', 'true')
      console.log('PWA was installed')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice

    if (outcome === 'accepted') {
      console.log('User accepted the install prompt')
      setIsInstalled(true)
      localStorage.setItem('pwa-installed', 'true')
    } else {
      console.log('User dismissed the install prompt')
    }

    setDeferredPrompt(null)
    setShowInstallPrompt(false)
    localStorage.setItem('pwa-install-prompted', 'true')
  }

  const handleDismiss = (e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    setShowInstallPrompt(false)
    setDeferredPrompt(null)
    localStorage.setItem('pwa-install-prompted', 'true')
  }

  // Show manual install button if no automatic prompt but not installed
  if (isInstalled) {
    return null
  }

  // If no deferred prompt but not installed, show manual instructions
  if (!showInstallPrompt || !deferredPrompt) {
    const isMobile = typeof navigator !== 'undefined' && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    
    // Double-check if already installed before showing manual prompt
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isIOSStandalone = (window.navigator as any).standalone === true
    
    if (isMobile && !localStorage.getItem('pwa-manual-install-shown') && !isStandalone && !isIOSStandalone) {
      return (
        <div className="fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto">
          <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <Download size={20} className="text-purple-600" />
              </div>

              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-800 text-sm mb-1">
                  Install OnlyDiary App
                </h3>
                <p className="text-xs text-gray-600 mb-3 leading-relaxed">
                  For the best experience, add OnlyDiary to your home screen:
                </p>
                <ol className="text-xs text-gray-600 space-y-1 mb-3">
                  <li>1. Tap the share button in your browser</li>
                  <li>2. Select "Add to Home Screen"</li>
                  <li>3. Tap "Add" to install</li>
                </ol>

                <button
                  onClick={() => {
                    localStorage.setItem('pwa-manual-install-shown', 'true')
                    localStorage.setItem('pwa-install-dismissed-permanently', 'true')
                    setShowInstallPrompt(false)
                  }}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Got it, don't show again
                </button>
              </div>

              <button
                onClick={() => {
                  localStorage.setItem('pwa-manual-install-shown', 'true')
                  setShowInstallPrompt(false)
                }}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        </div>
      )
    }

    return null
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-[9999] max-w-sm mx-auto pointer-events-auto">
      <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-4 relative">
        {/* Close button in top right corner */}
        <button
          onClick={(e) => handleDismiss(e)}
          className="absolute -top-2 -right-2 w-8 h-8 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-colors z-10 shadow-lg"
          type="button"
          aria-label="Close install prompt"
        >
          <X size={14} />
        </button>

        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <Download size={20} className="text-purple-600" />
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-800 text-sm mb-1">
              Install OnlyDiary App
            </h3>
            <p className="text-xs text-gray-600 mb-3 leading-relaxed">
              Get the full experience! Install OnlyDiary as an app for offline reading,
              push notifications, and faster access.
            </p>

            <Button
              onClick={handleInstallClick}
              size="sm"
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white text-xs"
            >
              Install App
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
