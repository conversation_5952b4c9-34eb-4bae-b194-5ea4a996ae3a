'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface AudioPlayerProps {
  audioUrl: string
  duration: number

  className?: string
  waveformData?: number[] // Optional pre-computed waveform
  postId?: string // For tracking plays
  onPlayCountUpdate?: (count: number) => void
}

export function AudioPlayer({
  audioUrl,
  duration,

  className = '',
  waveformData,
  postId,
  onPlayCountUpdate
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const animationRef = useRef<number | null>(null)

  // Generate simple waveform bars if no data provided - use fixed length to avoid hydration issues
  const waveform = waveformData || Array.from({ length: 30 }, (_, i) => {
    // Use a deterministic pattern based on index to avoid hydration mismatch
    const pattern = [0.3, 0.7, 0.5, 0.9, 0.4, 0.8, 0.6, 0.2, 0.9, 0.5]
    return pattern[i % pattern.length]
  })

  useEffect(() => {
    // Don't create audio element if no URL provided
    if (!audioUrl || audioUrl.trim() === '') {
      console.warn('AudioPlayer: No audio URL provided')
      return
    }

    console.log('AudioPlayer: Creating audio element with URL:', audioUrl)

    // Create audio element
    audioRef.current = new Audio()
    const audio = audioRef.current
    
    // Safari iOS needs specific attributes
    // Don't set crossOrigin unless we know CORS is configured
    audio.preload = 'auto'
    audio.volume = 1.0
    
    // Set source after attributes
    audio.src = audioUrl

    audio.addEventListener('canplay', () => {
      setIsLoading(false)
    })
    audio.addEventListener('error', (e) => {
      console.error('Audio playback error event:', e)
      if (audio.error) {
        console.error('Audio element error:', {
          message: audio.error.message || 'Unknown error',
          code: audio.error.code,
          MEDIA_ERR_ABORTED: audio.error.code === 1,
          MEDIA_ERR_NETWORK: audio.error.code === 2,
          MEDIA_ERR_DECODE: audio.error.code === 3,
          MEDIA_ERR_SRC_NOT_SUPPORTED: audio.error.code === 4,
          url: audioUrl,
          networkState: audio.networkState,
          readyState: audio.readyState,
          currentSrc: audio.currentSrc
        })
      }
      setIsLoading(false)
    })
    audio.addEventListener('timeupdate', () => {
      setCurrentTime(audio.currentTime)
    })
    audio.addEventListener('ended', () => {
      setIsPlaying(false)
      setCurrentTime(0)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    })



    return () => {
      if (audio) {
        audio.pause()
        audio.src = ''
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [audioUrl])

  const setupAudioContext = async () => {
    if (!audioRef.current || audioContextRef.current) return

    try {
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaElementSource(audioRef.current)
      source.connect(analyserRef.current)
      analyserRef.current.connect(audioContextRef.current.destination)
      analyserRef.current.fftSize = 256
    } catch (error) {
      console.error('Error setting up audio context:', error)
    }
  }

  const updateAudioLevel = () => {
    if (!analyserRef.current) return

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount)
    analyserRef.current.getByteFrequencyData(dataArray)
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length
    setAudioLevel(average / 255)

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(updateAudioLevel)
    }
  }

  const handlePlay = async () => {
    if (!audioRef.current || !audioUrl || audioUrl.trim() === '') {
      console.error('AudioPlayer: Cannot play - no audio element or URL')
      return
    }

    console.log('AudioPlayer: Play button clicked', { 
      audioUrl, 
      readyState: audioRef.current.readyState,
      networkState: audioRef.current.networkState 
    })

    try {
      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
        }
      } else {
        // Track play if postId is provided
        if (postId) {
          try {
            const response = await fetch(`/api/audio/posts/${postId}/play`, {
              method: 'POST'
            })
            if (response.ok) {
              const { play_count } = await response.json()
              onPlayCountUpdate?.(play_count)
            }
          } catch (error) {
            console.error('Failed to track audio play:', error)
          }
        }

        // For Safari iOS, we need to handle audio differently
        const isSafariIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent) && /Safari/i.test(navigator.userAgent)
        
        setIsLoading(true)
        
        try {
          if (isSafariIOS) {
            // Safari iOS specific handling
            audioRef.current.load()
            
            // Wait for loadeddata event
            await new Promise((resolve, reject) => {
              const timeout = setTimeout(() => {
                reject(new Error('Audio load timeout'))
              }, 5000)
              
              audioRef.current.addEventListener('loadeddata', () => {
                clearTimeout(timeout)
                resolve(true)
              }, { once: true })
              
              audioRef.current.addEventListener('error', () => {
                clearTimeout(timeout)
                reject(new Error('Audio load error'))
              }, { once: true })
            })
          }
          
          // Try to play
          const playPromise = audioRef.current.play()
          
          if (playPromise !== undefined) {
            await playPromise
            setIsPlaying(true)
            
            // Only setup audio context if not Safari iOS (causes issues there)
            if (!isSafariIOS) {
              await setupAudioContext()
              updateAudioLevel()
            }
          }
        } catch (playError) {
          console.error('Play error details:', {
            error: playError,
            readyState: audioRef.current.readyState,
            networkState: audioRef.current.networkState,
            currentSrc: audioRef.current.currentSrc,
            duration: audioRef.current.duration,
            paused: audioRef.current.paused
          })
          throw playError
        } finally {
          setIsLoading(false)
        }
      }
    } catch (error) {
      console.error('Error playing audio:', error)
      setIsPlaying(false)
    }
  }

  const handleSeek = (clickX: number, elementWidth: number) => {
    if (!audioRef.current) return
    
    const seekTime = (clickX / elementWidth) * duration
    audioRef.current.currentTime = seekTime
    setCurrentTime(seekTime)
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={`bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-50 rounded-2xl p-3 sm:p-4 md:p-6 border border-blue-100/50 shadow-inner ${className}`}>
      <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
        {/* Sleek Play/Pause Button */}
        <button
          onClick={handlePlay}
          disabled={isLoading}
          className="group relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 flex-shrink-0 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 text-white flex items-center justify-center p-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden"
        >
          {/* Animated background glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-400 via-purple-400 to-pink-400 rounded-full blur-md opacity-60 group-hover:opacity-80 transition-opacity duration-300 -z-10" />

          {/* Inner circle for depth */}
          <div className="absolute inset-1 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-full" />

          {/* Button content */}
          <div className="relative z-10 flex items-center justify-center">
            {isLoading ? (
              <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white/80 border-t-white rounded-full animate-spin" />
            ) : isPlaying ? (
              // Pause icon - two bars
              <div className="flex items-center gap-0.5 sm:gap-1">
                <div className="w-1 sm:w-1.5 h-3 sm:h-4 bg-white rounded-full" />
                <div className="w-1 sm:w-1.5 h-3 sm:h-4 bg-white rounded-full" />
              </div>
            ) : (
              // Play icon - triangle
              <div className="w-0 h-0 border-l-[6px] sm:border-l-[8px] border-l-white border-t-[4px] sm:border-t-[6px] border-t-transparent border-b-[4px] sm:border-b-[6px] border-b-transparent ml-0.5 sm:ml-1" />
            )}
          </div>

          {/* Ripple effect on click */}
          <div className="absolute inset-0 rounded-full bg-white/20 scale-0 group-active:scale-100 transition-transform duration-150" />
        </button>

        {/* Waveform Visualization */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <div
            className="flex items-center justify-between h-6 sm:h-8 cursor-pointer overflow-hidden w-full"
            onClick={(e) => {
              const rect = e.currentTarget.getBoundingClientRect()
              const clickX = e.clientX - rect.left
              handleSeek(clickX, rect.width)
            }}
          >
            {waveform.map((height, index) => {
              const barProgress = (index / waveform.length) * 100
              const isPassed = barProgress <= progress
              const isActive = isPlaying && Math.abs(barProgress - progress) < 2.5

              return (
                <div
                  key={index}
                  className={`flex-1 max-w-[4px] sm:max-w-[6px] mx-[1px] rounded-full transition-all duration-100 ${
                    isPassed
                      ? 'bg-blue-500'
                      : 'bg-gray-300'
                  } ${
                    isActive ? 'bg-blue-600' : ''
                  }`}
                  style={{
                    height: `${Math.max(3, height * 20)}px`,
                  }}
                />
              )
            })}
          </div>

          {/* Progress indicator */}
          <div className="flex justify-between text-xs sm:text-sm text-gray-500 mt-1 px-1">
            <span>{currentTime.toFixed(1)}s</span>
            <span>{duration.toFixed(1)}s</span>
          </div>
        </div>
      </div>
    </div>
  )
}
