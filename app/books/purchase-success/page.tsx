"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

interface PurchasedBook {
  id: string
  title: string
  cover_image_url: string
  users: {
    name: string
    avatar_url: string
  }
}

function PurchaseSuccessContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [book, setBook] = useState<PurchasedBook | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const sessionId = searchParams.get('session_id')
    const bookId = searchParams.get('book_id')

    if (!sessionId || !bookId) {
      setError('Invalid purchase session')
      setLoading(false)
      return
    }

    verifyPurchase(sessionId, bookId)
  }, [searchParams])

  const verifyPurchase = async (sessionId: string, bookId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Verify the purchase exists
      const { data: purchase, error: purchaseError } = await supabase
        .from('book_purchases')
        .select('id')
        .eq('user_id', user.id)
        .eq('project_id', bookId)
        .eq('stripe_session_id', sessionId)
        .single()

      if (purchaseError || !purchase) {
        setError('Purchase verification failed')
        setLoading(false)
        return
      }

      // Fetch book details
      const { data: bookData, error: bookError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          users!inner(name, avatar_url)
        `)
        .eq('id', bookId)
        .eq('is_ebook', true)
        .single()

      if (bookError) throw bookError

      setBook(bookData)
    } catch (error) {
      console.error('Error verifying purchase:', error)
      setError('Failed to verify purchase')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying your purchase...</p>
        </div>
      </div>
    )
  }

  if (error || !book) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="text-6xl mb-4">❌</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Purchase Error
          </h2>
          <p className="text-gray-600 mb-6">
            {error || 'We couldn\'t verify your purchase. Please contact support if you believe this is an error.'}
          </p>
          <div className="flex gap-3 justify-center">
            <Link href="/books">
              <Button variant="outline">Browse Books</Button>
            </Link>
            <Link href="/support">
              <Button>Contact Support</Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="max-w-2xl mx-auto px-4 py-16">
        
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🎉</div>
          <h1 className="text-3xl font-serif text-gray-900 mb-2">
            Purchase Successful!
          </h1>
          <p className="text-gray-600">
            Thank you for supporting independent authors
          </p>
        </div>

        {/* Book Card */}
        <Card className="border-purple-200 shadow-lg mb-8">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-20 h-32 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                {book.cover_image_url ? (
                  <img
                    src={book.cover_image_url}
                    alt={book.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    📚
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <h2 className="text-xl font-serif text-gray-900 mb-2">
                  {book.title}
                </h2>
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    {book.users.avatar_url ? (
                      <img
                        src={book.users.avatar_url}
                        alt={book.users.name}
                        className="w-6 h-6 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-xs text-purple-600 font-medium">
                        {book.users.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  <span className="text-gray-700">by {book.users.name}</span>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-green-800">
                    <span>✅</span>
                    <span className="font-medium">You now own this book!</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link href={`/books/${book.id}/read`} className="block">
            <Button size="lg" className="w-full bg-purple-600 text-white hover:bg-purple-700">
              📖 Start Reading Now
            </Button>
          </Link>
          
          <div className="grid grid-cols-2 gap-3">
            <Link href={`/books/${book.id}`}>
              <Button variant="outline" size="lg" className="w-full">
                📋 Book Details
              </Button>
            </Link>
            <Link href="/books">
              <Button variant="outline" size="lg" className="w-full">
                📚 Browse More Books
              </Button>
            </Link>
          </div>
        </div>

        {/* What's Next */}
        <Card className="mt-8 border-blue-200 bg-blue-50/50">
          <CardContent className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">What's next?</h3>
            <div className="space-y-3 text-sm text-gray-700">
              <div className="flex items-start gap-3">
                <span className="text-purple-600">📖</span>
                <div>
                  <strong>Start reading:</strong> Your book is ready to read on any device
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-purple-600">💾</span>
                <div>
                  <strong>Reading progress:</strong> Your bookmarks and progress are automatically saved
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-purple-600">⭐</span>
                <div>
                  <strong>Leave a review:</strong> Help other readers discover great books
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-purple-600">💌</span>
                <div>
                  <strong>Support the author:</strong> Your purchase directly supports independent writers
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Receipt Info */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>A receipt has been sent to your email address.</p>
          <p>Need help? <Link href="/support" className="text-purple-600 hover:underline">Contact our support team</Link></p>
        </div>
      </div>
    </div>
  )
}

export default function PurchaseSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <PurchaseSuccessContent />
    </Suspense>
  )
}
