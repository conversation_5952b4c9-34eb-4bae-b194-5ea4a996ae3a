"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { WithdrawalSection } from "@/components/WithdrawalSection"
import { StripeConnectButton } from "@/components/StripeConnectButton"
import { Button } from "@/components/ui/button"

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'


import { PriceEditor } from "@/components/PriceEditor"
import { EntriesManager } from "@/components/EntriesManager"
import { AudioPostsManager } from "@/components/AudioPostsManager"

import { InvitePrompt } from "@/components/InvitePrompt"
import { FirstPostModal } from "@/components/FirstPostModal"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

export default function WriterDashboard() {
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [stripeBalance, setStripeBalance] = useState<{
    available: number
    pending: number
    message?: string
  }>({ available: 0, pending: 0 })
  const [totalReactions, setTotalReactions] = useState(0)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()

    // Set up periodic refresh every 30 seconds
    const interval = setInterval(() => {
      if (!loading) {
        checkAuthAndFetchData()
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  // Minimal real-time subscriptions - only for critical updates
  useEffect(() => {
    if (!profile?.id) return

    const userId = profile.id

    // Debounce function to prevent excessive API calls
    let refreshTimeout: NodeJS.Timeout

    // REMOVED: Diary entries don't need real-time updates on dashboard
    // Users can refresh manually or we can use periodic refresh

    // REMOVED: Subscriptions don't need real-time updates on dashboard
    // These change infrequently and can be refreshed manually

    // Keep only NEW payments for immediate balance updates (important for creators)
    const paymentsSubscription = supabase
      .channel('dashboard-payments')
      .on('postgres_changes',
        {
          event: 'INSERT', // Only new payments, not updates
          schema: 'public',
          table: 'payments',
          filter: `writer_id=eq.${userId}`
        },
        () => {
          console.log('New payment received, refreshing data...')
          checkAuthAndFetchData() // Immediate refresh for payments
        }
      )
      .subscribe()

    // REMOVED: Audio posts don't need real-time updates on dashboard
    // Users can refresh manually

    // REMOVED: Audio reactions, loves, replies don't need real-time updates on dashboard
    // These are better handled with periodic refresh or manual refresh

    // REMOVED: Book projects don't need real-time updates on dashboard
    // Projects change infrequently and can be refreshed manually

    return () => {
      paymentsSubscription.unsubscribe()
      clearTimeout(refreshTimeout)
    }
  }, [profile?.id])

  // Load reaction counts for all entries
  useEffect(() => {
    const loadReactionCounts = async () => {
      if (!profile?.entries || profile.entries.length === 0) {
        setTotalReactions(0)
        return
      }

      try {
        let total = 0
        for (const entry of profile.entries) {
          const { data: reactionCounts } = await supabase
            .rpc('get_reaction_counts', { entry_id: entry.id })

          if (reactionCounts) {
            const entryTotal = reactionCounts.reduce((sum: number, reaction: any) => sum + reaction.count, 0)
            total += entryTotal
          }
        }
        setTotalReactions(total)
      } catch (error) {
        console.error('Error loading reaction counts:', error)
        setTotalReactions(0)
      }
    }

    loadReactionCounts()
  }, [profile?.entries, supabase])

  const checkAuthAndFetchData = async () => {
    try {
      // Check if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .single()

      if (profileError || !profileData) {
        console.log('Profile error or no profile:', profileError, profileData)
        router.push('/')
        return
      }

      console.log('User profile:', { id: profileData.id, role: profileData.role, name: profileData.name })

      // All authenticated users can access dashboard (unified experience)
      if (!profileData || (profileData.role !== 'user' && profileData.role !== 'admin')) {
        console.log('Access denied - invalid role:', profileData?.role)
        router.push('/login')
        return
      }

      console.log('Dashboard access granted for role:', profileData.role)

      // Get writer's diary entries
      const { data: entries } = await supabase
        .from("diary_entries")
        .select("id, title, body_md, created_at, is_free, is_hidden, view_count")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get writer's audio posts (admin sees all, regular users see their own)
      let audioQuery = supabase
        .from("audio_posts")
        .select(`
          id,
          audio_url,
          description,
          duration_seconds,
          love_count,
          reply_count,
          created_at,
          reactions(
            id,
            reaction_type,
            created_at,
            user:users(name, avatar, profile_picture_url)
          ),
          audio_replies:audio_replies(
            id,
            audio_url,
            duration_seconds,
            love_count,
            created_at,
            user:users(name, avatar, profile_picture_url)
          )
        `)
        .order("created_at", { ascending: false })

      // Regular users only see their own posts
      if (profileData.role !== 'admin') {
        audioQuery = audioQuery.eq("user_id", user.id)
      }

      const { data: audioPosts, error: audioError } = await audioQuery

      console.log('Audio posts fetch result:', {
        audioPosts,
        audioError,
        userRole: profileData.role,
        userIdUsed: profileData.role === 'admin' ? 'ALL' : user.id
      })

      // Get writer's book projects
      const { data: bookProjects } = await supabase
        .from("projects")
        .select("id, title, description, created_at, is_private, total_chapters, total_words")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get subscription count
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select("id, subscriber_id, active_until")
        .eq("writer_id", user.id)
        .gte("active_until", new Date().toISOString())

      // Get payments for earnings calculation
      const { data: payments } = await supabase
        .from("payments")
        .select("amount_cents, kind, created_at, stripe_payment_id")
        .eq("writer_id", user.id)
        .order("created_at", { ascending: false })

      console.log('All payments:', payments)

      // Get withdrawals for balance calculation
      const { data: withdrawals } = await supabase
        .from("withdrawals")
        .select("amount_cents, status")
        .eq("writer_id", user.id)
        .neq("status", "rejected")

      setProfile({
        ...profileData,
        entries: entries || [],
        audioPosts: audioPosts || [],
        bookProjects: bookProjects || [],
        subscriptions: subscriptions || [],
        payments: payments || [],
        withdrawals: withdrawals || []
      })

      // Fetch real Stripe Connect balance
      await fetchStripeBalance()
    } catch (error) {
      console.error('Error in checkAuthAndFetchData:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const fetchStripeBalance = async () => {
    try {
      console.log('Fetching Stripe balance...')
      const response = await fetch('/api/stripe/balance')
      const data = await response.json()

      console.log('Stripe balance response:', { status: response.status, data })

      if (response.ok) {
        setStripeBalance({
          available: data.available,
          pending: data.pending,
          message: data.message
        })
        console.log('Stripe balance set:', data.available)
      } else {
        console.error('Error fetching Stripe balance:', data.error)
        setStripeBalance({ available: 0, pending: 0, message: data.error })
      }
    } catch (error) {
      console.error('Error fetching Stripe balance:', error)
      setStripeBalance({ available: 0, pending: 0, message: 'Failed to fetch balance' })
    }
  }

  const handleNavigation = async (path: string) => {
    setNavigating(path)
    router.push(path)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Dashboard...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load dashboard</p>
        </div>
      </div>
    )
  }

  const activeSubscriberCount = profile.subscriptions?.length || 0
  const monthlyRevenue = activeSubscriberCount * (profile.price_monthly || 0)

  // Count ALL content types
  const diaryEntriesCount = profile.entries?.length || 0
  const audioPostsCount = profile.audioPosts?.length || 0
  const bookProjectsCount = profile.bookProjects?.length || 0
  const totalEntries = diaryEntriesCount + audioPostsCount + bookProjectsCount

  // Use real Stripe Connect balance instead of manual calculations
  const availableBalance = stripeBalance.available // This is the actual withdrawable amount from Stripe

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Mobile-First Header */}
        <div className="mb-6" data-tutorial="dashboard-header">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg sm:text-xl font-serif text-gray-900 mb-1 leading-tight">
                Welcome back, {profile.name || 'Writer'}
              </h1>
              <p className="text-gray-600 text-sm">
                Manage your writing and track your progress
              </p>
            </div>
            <Button
              onClick={() => checkAuthAndFetchData()}
              disabled={loading}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {loading ? '⟳' : '↻'} Refresh
            </Button>
          </div>
        </div>

        {/* Compact Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-2 mb-4">
          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Subscribers</h3>
            <p className="text-lg font-bold text-gray-900">{activeSubscriberCount}</p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Monthly Revenue</h3>
            <p className="text-lg font-bold text-gray-900">{formatPrice(monthlyRevenue)}</p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">All Content</h3>
            <p className="text-lg font-bold text-gray-900">{totalEntries}</p>
            <p className="text-xs text-gray-400 mt-0.5">
              {diaryEntriesCount}d • {audioPostsCount}a • {bookProjectsCount}b
            </p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Reactions</h3>
            <p className="text-lg font-bold text-gray-900">{totalReactions.toLocaleString()}</p>
          </div>

          {/* Prominent Financial Info */}
          <div className="rounded-xl p-4 bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 shadow-sm xs:col-span-2 md:col-span-3 lg:col-span-1">
            <div className="text-center">
              <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-2">Total Earnings</h3>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {formatPrice(availableBalance + stripeBalance.pending)}
              </div>

              <div className="flex justify-center space-x-4 text-sm mb-4">
                <div className="text-center">
                  <div className="font-semibold text-green-700">{formatPrice(availableBalance)}</div>
                  <div className="text-xs text-green-600">Available</div>
                </div>
                {stripeBalance.pending > 0 && (
                  <>
                    <div className="text-gray-300">|</div>
                    <div className="text-center">
                      <div className="font-semibold text-orange-700">{formatPrice(stripeBalance.pending)}</div>
                      <div className="text-xs text-orange-600">Pending</div>
                    </div>
                  </>
                )}
              </div>

              {availableBalance >= 1000 ? (
                <button
                  onClick={() => document.getElementById('withdrawal-section')?.scrollIntoView({ behavior: 'smooth' })}
                  className="w-full bg-green-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors min-h-[44px] flex items-center justify-center shadow-sm"
                >
                  💰 Withdraw Funds
                </button>
              ) : (
                <div className="w-full py-2.5 px-4 rounded-lg text-sm bg-gray-100 text-gray-600 min-h-[44px] flex items-center justify-center">
                  {availableBalance > 0
                    ? `$${((1000 - availableBalance) / 100).toFixed(2)} more needed`
                    : '⏳ Processing payments...'
                  }
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile-First Content Sections */}
        <div className="space-y-4 sm:space-y-6">
          {/* Quick Actions - Mobile-First Full Width */}
          <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
            <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-3 px-1">Quick Actions</h2>

            {/* Primary Actions - Mobile-First Full Width */}
            <div className="space-y-2 mb-3 sm:mb-4">
              <Button
                onClick={() => handleNavigation('/write')}
                disabled={navigating === '/write'}
                className="w-full bg-blue-600 text-white py-2.5 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/write' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span className="text-xs sm:text-sm">Creating...</span>
                  </>
                ) : (
                  <span className="text-xs sm:text-sm">📝 Create New Entry</span>
                )}
              </Button>

              <Button
                onClick={() => handleNavigation('/publishing')}
                disabled={navigating === '/publishing'}
                className="w-full bg-purple-600 text-white py-2.5 px-3 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/publishing' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span className="text-xs sm:text-sm">Loading...</span>
                  </>
                ) : (
                  <span className="text-xs sm:text-sm">📚 Publishing Center</span>
                )}
              </Button>
            </div>

            {/* Secondary Actions - Mobile-First Stacked */}
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
              <Button
                onClick={() => handleNavigation('/profile')}
                disabled={navigating === '/profile'}
                className="w-full bg-gray-100 text-gray-700 py-2.5 px-3 rounded-md text-xs sm:text-sm hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/profile' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>Edit Profile</span>
                )}
              </Button>

              <Button
                onClick={() => handleNavigation(`/u/${profile.id}`)}
                disabled={navigating === `/u/${profile.id}`}
                className="w-full bg-gray-100 text-gray-700 py-2.5 px-3 rounded-md text-xs sm:text-sm hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === `/u/${profile.id}` ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>View Profile</span>
                )}
              </Button>
            </div>
          </div>

          {/* Profile Settings */}
          <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
            <div className="flex items-center gap-2 mb-4">
              <h2 className="text-base sm:text-lg font-serif text-gray-800 flex-1 min-w-0 px-1">Profile Settings</h2>
              <Link
                href="/profile"
                className="bg-purple-600 text-white px-2 py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-purple-700 transition-colors min-h-[40px] flex items-center justify-center shrink-0"
              >
                Edit
              </Link>
            </div>

            <div className="space-y-4">
              <div>
                <PriceEditor
                  initialPrice={profile.price_monthly || 999}
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 block mb-2">Payment Processing</label>
                <StripeConnectButton
                  isConnected={!!profile.stripe_account_id}
                  onboardingComplete={profile.stripe_onboarding_complete || false}
                />
                {!profile.stripe_account_id && (
                  <p className="text-xs text-gray-600 mt-2">
                    Connect Stripe to receive payments from subscribers.
                  </p>
                )}
              </div>

              <div className="w-full overflow-hidden">
                <label className="text-sm font-medium text-gray-700 block mb-2">Your Profile URL</label>
                <div className="bg-gray-50 p-2 rounded border w-full overflow-hidden">
                  <code className="text-xs text-gray-800 block w-full break-words overflow-wrap-anywhere">
                    onlydiary.app/{profile.custom_url || `u/${profile.id}`}
                  </code>
                </div>
                <div className="grid grid-cols-1 gap-2 mt-2 sm:grid-cols-2">
                  <Link
                    href={profile.custom_url ? `/${profile.custom_url}` : `/u/${profile.id}`}
                    className="bg-blue-600 text-white px-1 py-2 rounded text-xs font-medium hover:bg-blue-700 transition-colors text-center min-h-[40px] flex items-center justify-center"
                  >
                    <span className="truncate">View Profile</span>
                  </Link>
                  {!profile.custom_url ? (
                    <Link
                      href="/profile/edit"
                      className="bg-gray-100 text-gray-700 px-1 py-2 rounded text-xs font-medium hover:bg-gray-200 transition-colors text-center min-h-[40px] flex items-center justify-center border border-gray-200"
                    >
                      <span className="truncate">Set Custom URL</span>
                    </Link>
                  ) : (
                    <div className="bg-gray-50 px-1 py-2 rounded text-xs text-gray-500 text-center min-h-[40px] flex items-center justify-center border border-gray-200">
                      <span className="truncate">Custom URL Set</span>
                    </div>
                  )}
                </div>
              </div>

              {profile.bio && (
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">Bio</label>
                  <p className="text-gray-700 text-sm bg-gray-50 p-2 rounded break-words">{profile.bio}</p>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Marketing & Revenue Section - Combined */}
        <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-lg p-3 sm:p-4 border border-purple-100">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">📧</span>
            </div>
            <div>
              <h2 className="text-base font-serif text-gray-800">Marketing & Revenue</h2>
              <p className="text-xs text-gray-600">Grow your audience and boost earnings</p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
            {/* Mailing List */}
            <div className="bg-white rounded-lg p-3 border border-gray-100 flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm">📬</span>
                <h3 className="text-sm font-medium text-gray-800">Mailing List</h3>
              </div>
              <div className="flex-1 mb-3">
                <p className="text-xs text-gray-600">Build your email audience</p>
              </div>
              <Button
                onClick={() => handleNavigation('/dashboard/mailing')}
                disabled={navigating === '/dashboard/mailing'}
                className="w-full bg-blue-600 text-white py-2 px-3 rounded text-xs font-medium hover:bg-blue-700 transition-colors"
              >
                {navigating === '/dashboard/mailing' ? 'Loading...' : 'Manage List'}
              </Button>
            </div>

            {/* Push Notifications */}
            <div className="bg-white rounded-lg p-3 border border-gray-100 flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm">🔔</span>
                <h3 className="text-sm font-medium text-gray-800">Push Notifications</h3>
              </div>
              <div className="flex-1 mb-3">
                <p className="text-xs text-gray-600">$0.05 per notification</p>
                <p className="text-xs text-purple-600 font-medium">Boost revenue instantly</p>
              </div>
              <Button
                onClick={() => handleNavigation('/dashboard/notifications')}
                disabled={navigating === '/dashboard/notifications'}
                className="w-full bg-purple-600 text-white py-2 px-3 rounded text-xs font-medium hover:bg-purple-700 transition-colors"
              >
                {navigating === '/dashboard/notifications' ? 'Loading...' : 'Get Started'}
              </Button>
            </div>
          </div>
        </div>

        {/* Withdrawal Section */}
        <div id="withdrawal-section">
          <WithdrawalSection
            availableBalance={availableBalance}
            pendingBalance={stripeBalance.pending}
            userId={profile.id}
          />
        </div>

        {/* Invite Friends Prompt */}
        <InvitePrompt variant="card" userName={profile.name} />

        {/* Manage Entries */}
        <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
          <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-4 px-1">Manage Entries</h2>
          <EntriesManager initialEntries={profile.entries || []} />
        </div>

        {/* Manage Audio Posts */}
        <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
          <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-4 px-1">Manage Audio Posts</h2>
          <AudioPostsManager initialPosts={profile.audioPosts || []} />
        </div>

      </div>
    </div>
  )
}
