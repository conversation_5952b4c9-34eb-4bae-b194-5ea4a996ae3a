'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"
import Image from "next/image"
import { SmartTypography } from "@/components/SmartTypography"
import { PaywallContent } from "@/components/PaywallContent"
import { getSubscriptionStatus, getProjectPurchaseStatus } from "@/lib/paywall"

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

interface Project {
  id: string
  user_id: string
  title: string
  description: string | null
  cover_image_url: string | null
  genre: string | null
  is_private: boolean
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number | null
  total_chapters: number
  total_words: number
  created_at: string
  updated_at: string
}

interface Chapter {
  id: string
  project_id: string
  user_id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
  is_published: boolean
  love_count: number
  created_at: string
  updated_at: string
}

interface Writer {
  id: string
  name: string
  bio: string | null
  avatar: string | null
}

export default function ChapterPage({ params }: { params: Promise<{ id: string; chapterId: string }> }) {
  const [project, setProject] = useState<Project | null>(null)
  const [chapter, setChapter] = useState<Chapter | null>(null)
  const [writer, setWriter] = useState<Writer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [hasAccess, setHasAccess] = useState(false)
  const [user, setUser] = useState<any>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const loadChapter = async () => {
      try {
        const resolvedParams = await params
        const { id: projectId, chapterId } = resolvedParams

        // Load chapter details
        const { data: chapterData, error: chapterError } = await supabase
          .from("chapters")
          .select("*")
          .eq("id", chapterId)
          .eq("is_published", true) // Only published chapters
          .single()

        if (chapterError || !chapterData) {
          setError("Chapter not found or not published")
          setLoading(false)
          return
        }

        setChapter(chapterData)

        // Load project details
        const { data: projectData, error: projectError } = await supabase
          .from("projects")
          .select("*")
          .eq("id", projectId)
          .eq("is_private", false) // Only public projects
          .single()

        if (projectError || !projectData) {
          setError("Project not found or is private")
          setLoading(false)
          return
        }

        setProject(projectData)

        // Load writer info
        const { data: writerData, error: writerError } = await supabase
          .from("users")
          .select("id, name, bio, avatar")
          .eq("id", projectData.user_id)
          .single()

        if (writerError || !writerData) {
          setError("Writer information not found")
          setLoading(false)
          return
        }

        setWriter(writerData)

        // Check user authentication and access
        const { data: { user: authUser } } = await supabase.auth.getUser()
        setUser(authUser)

        if (authUser) {
          // Check if user is the owner
          const isOwner = authUser.id === projectData.user_id

          // Check if project is free
          const isFree = !projectData.price_amount

          let userHasAccess = isOwner || isFree

          if (!userHasAccess && projectData.price_amount) {
            // Check subscription status
            const hasSubscription = await getSubscriptionStatus(
              supabase,
              authUser.id,
              projectData.user_id
            )

            // Check project purchase status
            const hasPurchase = await getProjectPurchaseStatus(
              supabase,
              authUser.id,
              projectData.id,
              projectData.user_id
            )

            userHasAccess = hasSubscription || hasPurchase
          }

          setHasAccess(userHasAccess)
        } else {
          // Non-authenticated users only get access to free content
          setHasAccess(!projectData.price_amount)
        }

      } catch (error) {
        console.error("Error loading chapter:", error)
        setError("Failed to load chapter")
      } finally {
        setLoading(false)
      }
    }

    loadChapter()
  }, [params, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading chapter...</div>
      </div>
    )
  }

  if (error || !chapter || !project || !writer) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-gray-800 mb-4">Chapter Not Found</h1>
          <p className="text-gray-600 mb-6">{error || "This chapter doesn't exist or is not published."}</p>
          <Link href="/" className="text-blue-600 hover:text-blue-700 font-medium">
            ← Back to Home
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8">
        
        {/* Navigation */}
        <div className="mb-8">
          <Link 
            href={`/projects/${project.id}`} 
            className="text-blue-600 hover:text-blue-700 font-medium mb-2 inline-block"
          >
            ← Back to {project.title}
          </Link>
          <div className="text-sm text-gray-500">
            by <Link href={`/${writer.name}`} className="text-blue-600 hover:text-blue-700">{writer.name}</Link>
          </div>
        </div>

        {/* Chapter Content */}
        <article className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          
          {/* Chapter Header */}
          <div className="p-6 sm:p-8 border-b border-gray-200">
            <div className="text-sm text-blue-600 font-medium mb-2">
              Chapter {chapter.chapter_number}
            </div>
            <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-4">
              {chapter.title}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{chapter.word_count.toLocaleString()} words</span>
              <span>•</span>
              <span>{Math.ceil(chapter.word_count / 200)} min read</span>
              <span>•</span>
              <span>{new Date(chapter.created_at).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Chapter Content with Paywall */}
          <div className="p-6 sm:p-8">
            <PaywallContent
              content={chapter.content}
              photos={[]} // Chapters typically don't have photos, but could be extended
              isFree={!project.price_amount}
              hasAccess={hasAccess}
              writerName={writer.name}
              writerId={writer.id}
              projectId={project.id}
              type="chapter"
            />
          </div>

          {/* Chapter Footer */}
          <div className="p-6 sm:p-8 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                  {writer.avatar ? (
                    <Image
                      src={writer.avatar}
                      alt={writer.name}
                      width={40}
                      height={40}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    writer.name.charAt(0).toUpperCase()
                  )}
                </div>
                <div>
                  <div className="font-medium text-gray-800">{writer.name}</div>
                  <div className="text-sm text-gray-500">Author</div>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                {hasAccess && (
                  <button className="flex items-center gap-2 text-gray-600 hover:text-red-500 transition-colors">
                    <span>❤️</span>
                    <span className="text-sm">{chapter.love_count}</span>
                  </button>
                )}
                <Link
                  href={`/u/${writer.id}`}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  {hasAccess ? `More from ${writer.name}` : `Subscribe to ${writer.name}`}
                </Link>
              </div>
            </div>
          </div>
        </article>

        {/* Project Info */}
        <div className="mt-8 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-start gap-4">
            <div className="w-16 h-20 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg overflow-hidden flex-shrink-0">
              {project.cover_image_url ? (
                <Image
                  src={project.cover_image_url}
                  alt={project.title}
                  width={64}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-2xl opacity-50">📖</span>
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-serif text-lg text-gray-800 mb-1">{project.title}</h3>
              {project.description && (
                <p className="text-gray-600 text-sm mb-2 line-clamp-2">{project.description}</p>
              )}
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>{project.total_chapters} chapters</span>
                <span>•</span>
                <span>{project.total_words.toLocaleString()} words</span>
                {project.genre && (
                  <>
                    <span>•</span>
                    <span>{project.genre}</span>
                  </>
                )}
              </div>
            </div>
            <Link
              href={`/projects/${project.id}`}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
            >
              View All Chapters
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
