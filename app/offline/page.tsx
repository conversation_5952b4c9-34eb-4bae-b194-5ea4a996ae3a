'use client'

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

export default function OfflinePage() {
  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md mx-auto text-center">
        <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-4xl">📱</span>
        </div>

        <h1 className="text-2xl font-serif text-gray-800 mb-4">
          You're Offline
        </h1>

        <p className="text-gray-600 mb-6 leading-relaxed">
          No internet connection detected. Some features may be limited, but you can still
          read your downloaded content and drafts.
        </p>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-800 mb-2">Available Offline:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Read downloaded diary entries</li>
            <li>• Access your book library</li>
            <li>• Write new drafts</li>
            <li>• Browse cached content</li>
          </ul>
        </div>

        <button
          onClick={handleRetry}
          className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  )
}


