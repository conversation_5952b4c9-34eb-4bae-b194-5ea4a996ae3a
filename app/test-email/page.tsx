"use client"

import { useState } from 'react'

import { Button } from '@/components/ui/button'
n// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'


export default function TestEmailPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [status, setStatus] = useState<any>(null)

  const checkStatus = async () => {
    try {
      const response = await fetch('/api/test-email')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      setStatus({ error: 'Failed to check status' })
    }
  }

  const sendTestEmail = async () => {
    if (!email) {
      alert('Please enter an email address')
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testEmail: email })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to send test email' })
    } finally {
      setLoading(false)
    }
  }

  const sendDebugTest = async () => {
    if (!email) {
      alert('Please enter an email address')
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/debug-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testEmail: email })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to send debug email' })
    } finally {
      setLoading(false)
    }
  }

  const sendWelcomeTest = async () => {
    if (!email) {
      alert('Please enter an email address')
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/send-welcome-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'test-user-id',
          userEmail: email,
          userName: 'Test User',
          userRole: 'writer'
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to send welcome email' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 py-12 px-6">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-6">Email System Test</h1>
          
          {/* Status Check */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium">System Status</h2>
              <Button onClick={checkStatus} variant="outline" size="sm">
                Check Status
              </Button>
            </div>
            
            {status && (
              <div className="text-sm">
                <div className={`mb-2 ${status.resendConnected ? 'text-green-600' : 'text-red-600'}`}>
                  Resend: {status.resendConnected ? '✅ Connected' : '❌ Not Connected'}
                </div>
                <div className="text-gray-600">
                  From Email: {status.fromEmail || 'Not configured'}
                </div>
                {status.error && (
                  <div className="text-red-600 mt-2">
                    Error: {status.error}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Test Email Form */}
          <div className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Test Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              />
            </div>

            <div className="flex gap-4">
              <Button
                onClick={sendTestEmail}
                isLoading={loading}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                Send Test Email
              </Button>
              
              <Button
                onClick={sendWelcomeTest}
                isLoading={loading}
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Send Welcome Email
              </Button>
            </div>

            {/* Results */}
            {result && (
              <div className={`p-4 rounded-lg ${
                result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <h3 className={`font-medium mb-2 ${
                  result.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {result.success ? '✅ Success!' : '❌ Failed'}
                </h3>
                
                <div className={`text-sm ${
                  result.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {result.message && <div className="mb-1">{result.message}</div>}
                  {result.error && <div className="mb-1">Error: {result.error}</div>}
                  {result.details && <div className="mb-1">Details: {result.details}</div>}
                  {result.id && <div className="text-xs text-gray-500">ID: {result.id}</div>}
                </div>
              </div>
            )}
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-medium text-gray-800 mb-2">Instructions:</h3>
            <ol className="text-sm text-gray-600 space-y-1">
              <li>1. First, click "Check Status" to verify Resend connection</li>
              <li>2. Enter your email address</li>
              <li>3. Click "Send Test Email" to test basic functionality</li>
              <li>4. Click "Send Welcome Email" to test the welcome template</li>
              <li>5. Check your inbox (and spam folder) for the emails</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
