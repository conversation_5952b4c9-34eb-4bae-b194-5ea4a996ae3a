"use client"

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'

import { Button } from '@/components/ui/button'
n// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'


function InviteFriendsContent() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const searchParams = useSearchParams()
  const referrer = searchParams.get('ref') || 'friend'

  const inviteLink = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'}/register?ref=${referrer}`
  const inviteMessage = `Hey! I'm sharing my personal stories on OnlyDiary - where life becomes literature. Join me: ${inviteLink}`

  const handleSMSInvite = async () => {
    setLoading(true)
    try {
      // Open SMS with pre-filled message
      window.open(`sms:?body=${encodeURIComponent(inviteMessage)}`)
      setMessage('SMS app opened! Send the message to invite your friends.')
    } catch (error) {
      setMessage('Could not open SMS app. Try copying the message instead.')
    } finally {
      setLoading(false)
    }
  }

  const handleCopyMessage = async () => {
    setLoading(true)
    try {
      await navigator.clipboard.writeText(inviteMessage)
      setMessage('Invite message copied to clipboard! Paste it anywhere to share.')
    } catch (error) {
      setMessage('Could not copy to clipboard. Try selecting and copying the message below.')
    } finally {
      setLoading(false)
    }
  }

  const handleEmailInvite = () => {
    const subject = 'Join me on OnlyDiary'
    const body = inviteMessage
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`)
    setMessage('Email app opened! Send to invite your friends.')
  }

  const handleWhatsAppInvite = () => {
    window.open(`https://wa.me/?text=${encodeURIComponent(inviteMessage)}`)
    setMessage('WhatsApp opened! Send to invite your friends.')
  }

  const handleTwitterInvite = () => {
    const tweetText = `I'm sharing my personal stories on OnlyDiary - where life becomes literature. Join me: ${inviteLink}`
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`)
    setMessage('Twitter opened! Tweet to invite your friends.')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 py-12 px-6">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-serif text-gray-800 mb-2">Invite Your Friends</h1>
            <p className="text-gray-600 text-lg">OnlyDiary is better with friends! Share authentic stories together.</p>
          </div>

          {/* Referrer Info */}
          {referrer !== 'friend' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <p className="text-blue-800 text-center">
                <strong>{referrer}</strong> invited you to join OnlyDiary! Now invite your friends too.
              </p>
            </div>
          )}

          {/* Quick Share Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <Button
              onClick={handleSMSInvite}
              isLoading={loading}
              className="bg-green-600 text-white hover:bg-green-700 py-4 text-lg"
            >
              📱 Send via SMS
            </Button>

            <Button
              onClick={handleWhatsAppInvite}
              variant="outline"
              className="border-green-500 text-green-600 hover:bg-green-50 py-4 text-lg"
            >
              💬 Share on WhatsApp
            </Button>

            <Button
              onClick={handleEmailInvite}
              variant="outline"
              className="border-blue-500 text-blue-600 hover:bg-blue-50 py-4 text-lg"
            >
              ✉️ Send via Email
            </Button>

            <Button
              onClick={handleTwitterInvite}
              variant="outline"
              className="border-blue-400 text-blue-500 hover:bg-blue-50 py-4 text-lg"
            >
              🐦 Tweet Invite
            </Button>
          </div>

          {/* Copy Message */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-800">Or copy this message:</h3>
              <Button
                onClick={handleCopyMessage}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                📋 Copy
              </Button>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border">
              <p className="text-gray-700 text-sm leading-relaxed">
                {inviteMessage}
              </p>
            </div>
          </div>

          {/* Status Message */}
          {message && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800 text-center">{message}</p>
            </div>
          )}

          {/* Benefits */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-medium text-gray-800 mb-4 text-center">Why invite friends to OnlyDiary?</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4">
                <div className="text-2xl mb-2">📖</div>
                <p className="text-sm text-gray-600">Discover authentic stories from people you know</p>
              </div>
              <div className="p-4">
                <div className="text-2xl mb-2">💝</div>
                <p className="text-sm text-gray-600">Support your friends' creative journeys</p>
              </div>
              <div className="p-4">
                <div className="text-2xl mb-2">🌟</div>
                <p className="text-sm text-gray-600">Build a community of storytellers</p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <a 
              href={process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'} 
              className="text-gray-500 hover:text-gray-700 text-sm"
            >
              ← Back to OnlyDiary
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function InviteFriendsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 py-12 px-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invite page...</p>
        </div>
      </div>
    }>
      <InviteFriendsContent />
    </Suspense>
  )
}
