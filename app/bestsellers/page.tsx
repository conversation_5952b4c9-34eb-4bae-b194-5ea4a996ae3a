"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { CleanBestsellerChart } from "@/components/CleanBestsellerChart"

// Prevent static generation for this page since it uses client components
export const dynamic = 'force-dynamic'

export default function BestsellersPage() {

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center">
            <Link
              href="/books"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              ← Back to All Books
            </Link>
          </div>
        </div>
      </div>

      {/* Bestsellers Chart */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CleanBestsellerChart
          showHeader={true}
          limit={50}
        />

      </div>
    </div>
  )
}
