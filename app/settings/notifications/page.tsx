"use client"

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

interface EmailPreferences {
  welcome_emails: boolean
  new_post_notifications: boolean
  weekly_digest: boolean
  password_reset: boolean
}

export default function NotificationSettingsPage() {
  const [user, setUser] = useState<any>(null)
  const [preferences, setPreferences] = useState<EmailPreferences>({
    welcome_emails: true,
    new_post_notifications: true,
    weekly_digest: true,
    password_reset: true
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  
  const supabase = createSupabaseClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        setUser(user)
        
        // Get user's email preferences from database
        const { data: profile } = await supabase
          .from('users')
          .select('email_preferences')
          .eq('id', user.id)
          .single()
        
        if (profile?.email_preferences) {
          setPreferences(profile.email_preferences)
        }
      }
      setLoading(false)
    }
    
    getUser()
  }, [supabase])

  const handleSave = async () => {
    if (!user) return
    
    setSaving(true)
    setMessage('')
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ email_preferences: preferences })
        .eq('id', user.id)
      
      if (error) throw error
      
      setMessage('Email preferences saved successfully!')
    } catch (error) {
      console.error('Error saving preferences:', error)
      setMessage('Failed to save preferences. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleTestEmail = async () => {
    if (!user?.email) return
    
    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testEmail: user.email })
      })
      
      const result = await response.json()
      
      if (result.success) {
        setMessage('Test email sent! Check your inbox.')
      } else {
        setMessage('Failed to send test email: ' + result.error)
      }
    } catch (error) {
      setMessage('Failed to send test email.')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-gray-800 mb-4">Please sign in</h1>
          <a href="/login" className="text-blue-600 hover:underline">Go to login</a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 py-12 px-6">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-serif text-gray-800 mb-2">Email Notifications</h1>
            <p className="text-gray-600">Manage how you receive updates from OnlyDiary</p>
          </div>

          <div className="space-y-6">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-800">Welcome Emails</h3>
                <p className="text-sm text-gray-600">Get a welcome message when you join</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.welcome_emails}
                onChange={(e) => setPreferences(prev => ({ ...prev, welcome_emails: e.target.checked }))}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-800">New Post Notifications</h3>
                <p className="text-sm text-gray-600">Get notified when creators you follow publish new content</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.new_post_notifications}
                onChange={(e) => setPreferences(prev => ({ ...prev, new_post_notifications: e.target.checked }))}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-800">Weekly Digest</h3>
                <p className="text-sm text-gray-600">Get a summary of new content once per week</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.weekly_digest}
                onChange={(e) => setPreferences(prev => ({ ...prev, weekly_digest: e.target.checked }))}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-800">Security Emails</h3>
                <p className="text-sm text-gray-600">Password resets and security notifications (recommended)</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.password_reset}
                onChange={(e) => setPreferences(prev => ({ ...prev, password_reset: e.target.checked }))}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
            </div>
          </div>

          {message && (
            <div className={`mt-6 p-4 rounded-lg ${
              message.includes('success') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
            }`}>
              {message}
            </div>
          )}

          <div className="mt-8 flex gap-4">
            <Button
              onClick={handleSave}
              isLoading={saving}
              className="bg-gray-800 text-white hover:bg-gray-700"
            >
              Save Preferences
            </Button>
            
            <Button
              onClick={handleTestEmail}
              variant="outline"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Send Test Email
            </Button>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              <strong>Current email:</strong> {user.email}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              To change your email address, please contact support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
