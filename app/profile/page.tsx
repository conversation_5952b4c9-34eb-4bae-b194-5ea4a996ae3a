"use client"

import { useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

export default function ProfilePage() {
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const redirectToPublicProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser()

      if (user) {
        // Redirect to public profile view
        router.replace(`/u/${user.id}`)
      } else {
        // Redirect to login if not authenticated
        router.replace('/login')
      }
    }

    redirectToPublicProfile()
  }, [router, supabase])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        {/* Enhanced Loading Spinner */}
        <div className="relative inline-flex items-center justify-center mb-6">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"></div>
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent absolute"></div>
          <div className="absolute text-2xl">👤</div>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Your Profile...</h2>
        <p className="text-gray-600">Taking you to your profile page</p>
      </div>
    </div>
  )
}