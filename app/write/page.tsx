'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

// Prevent static generation for this page since it requires authentication
export const dynamic = 'force-dynamic'

export default function WriteSelection() {
  const [user, setUser] = useState<unknown>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<'diary' | 'book' | null>(null)
  const [showBookOptions, setShowBookOptions] = useState(false)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile) {
        router.push('/login')
        return
      }

      // All authenticated users can write (unified experience)
      if (profile.role !== 'user' && profile.role !== 'admin') {
        router.push('/timeline')
        return
      }

      setUser(profile)
      setLoading(false)
    }

    checkAuth()
  }, [router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) return null

  const handleNavigation = (type: 'diary' | 'book', path: string) => {
    if (type === 'book') {
      setShowBookOptions(true)
      return
    }

    setNavigating(type)
    router.push(path)
  }

  const handleBookOption = async (path: string) => {
    setNavigating('book')
    setShowBookOptions(false)

    // Small delay for visual feedback
    await new Promise(resolve => setTimeout(resolve, 300))

    router.push(path)
  }

  return (
    <div className="min-h-screen bg-white relative">
      {navigating && (
        <div className="absolute inset-0 bg-white/50 z-10 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
            <div className="flex items-center gap-3">
              <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
              <span className="text-gray-700 font-serif">
                {navigating === 'diary' ? 'Opening diary editor...' :
                 navigating === 'book' ? 'Opening book project editor...' :
                 navigating === 'audio' ? 'Opening audio recorder...' : 'Loading...'}
              </span>
            </div>
          </div>
        </div>
      )}
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-8 sm:py-12">

        {/* Clean Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-serif text-gray-900 mb-6">
            What would you like to write?
          </h1>
          <p className="text-gray-600 font-serif">
            Choose your format and start creating.
          </p>
        </div>

        {/* Clean Writing Options */}
        <div className="space-y-4">

          {/* Diary Entry */}
          <button
            onClick={() => handleNavigation('diary', '/write/diary')}
            disabled={navigating !== null}
            className="group block w-full text-left cursor-pointer"
          >
            <div className={`border border-gray-200 rounded-lg p-8 transition-colors ${
              navigating === null ? 'hover:border-gray-300' : ''
            } ${navigating === 'diary' ? 'border-gray-400 bg-gray-50' : ''}`}>
              <div className="flex items-center gap-4">
                <div className="text-3xl">📔</div>
                <div className="flex-1">
                  <h2 className="text-xl font-serif text-gray-900 mb-2">
                    Diary Entry
                  </h2>
                  <p className="text-gray-600 font-serif">
                    Share your daily thoughts and experiences
                  </p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                  {navigating === 'diary' ? (
                    <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                  ) : (
                    '→'
                  )}
                </div>
              </div>
            </div>
          </button>

          {/* Book Project */}
          <button
            onClick={() => handleNavigation('book', '/write/projects')}
            disabled={navigating !== null}
            className="group block w-full text-left cursor-pointer"
          >
            <div className={`border border-gray-200 rounded-lg p-8 transition-colors ${
              navigating === null ? 'hover:border-gray-300' : ''
            } ${showBookOptions ? 'border-purple-400 bg-purple-50' : ''}`}>
              <div className="flex items-center gap-4">
                <div className="text-3xl">📚</div>
                <div className="flex-1">
                  <h2 className="text-xl font-serif text-gray-900 mb-2">
                    Book Project
                  </h2>
                  <p className="text-gray-600 font-serif">
                    Create novels, stories, and longer works
                  </p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                  {navigating === 'book' ? (
                    <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                  ) : (
                    '→'
                  )}
                </div>
              </div>
            </div>
          </button>

          {/* Audio Post */}
          <button
            onClick={() => handleNavigation('audio', '/audio/create')}
            disabled={navigating !== null}
            className="group block w-full text-left cursor-pointer"
          >
            <div className={`border border-gray-200 rounded-lg p-8 transition-colors ${
              navigating === null ? 'hover:border-gray-300' : ''
            } ${navigating === 'audio' ? 'border-blue-400 bg-blue-50' : ''}`}>
              <div className="flex items-center gap-4">
                <div className="text-3xl">🎵</div>
                <div className="flex-1">
                  <h2 className="text-xl font-serif text-gray-900 mb-2">
                    Audio Post
                  </h2>
                  <p className="text-gray-600 font-serif">
                    Record a 9-second audio message
                  </p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                  {navigating === 'audio' ? (
                    <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                  ) : (
                    '→'
                  )}
                </div>
              </div>
            </div>
          </button>

          {/* Book Options Modal */}
          {showBookOptions && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
              <div className="bg-white rounded-2xl p-6 w-full max-w-md">
                <h2 className="text-xl font-serif text-gray-800 mb-4">Choose Book Option</h2>
                <p className="text-gray-600 font-serif mb-6">
                  Would you like to write a new book or publish an existing one?
                </p>

                <div className="space-y-3">
                  <button
                    onClick={() => handleBookOption('/write/projects')}
                    className="w-full p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors text-left"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">✏️</span>
                      <div>
                        <h3 className="font-medium text-gray-900">Write New Book</h3>
                        <p className="text-sm text-gray-600">Start a new writing project from scratch</p>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => handleBookOption('/write/upload-ebook')}
                    className="w-full p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors text-left"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">📖</span>
                      <div>
                        <h3 className="font-medium text-gray-900">Publish Existing Book</h3>
                        <p className="text-sm text-gray-600">Upload a completed PDF or EPUB file</p>
                      </div>
                    </div>
                  </button>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => setShowBookOptions(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

        </div>
      </div>
    </div>
  )
}
