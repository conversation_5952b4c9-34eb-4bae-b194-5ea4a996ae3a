import { createServerSupabaseClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import { AdminDashboard } from "@/components/admin/AdminDashboard"
import { AdminAuthCheck } from "@/components/admin/AdminAuthCheck"

// Prevent static generation for this page since it uses client components in layout
export const dynamic = 'force-dynamic'

export default async function AdminPage() {
  const supabase = await createServerSupabaseClient()

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError || !user) {
    redirect('/login')
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single()

  if (!profile || profile.role !== 'admin') {
    redirect('/dashboard')
  }

  // Get all creators with their Stripe status
  const { data: creators } = await supabase
    .from('users')
    .select(`
      id,
      name,
      email,
      role,
      stripe_account_id,
      stripe_onboarding_complete,
      price_monthly,
      created_at,
      updated_at
    `)
    .eq('role', 'writer')
    .order('created_at', { ascending: false })

  // Get subscription stats
  const { data: subscriptionStats } = await supabase
    .from('subscriptions')
    .select(`
      writer_id,
      status,
      created_at
    `)

  // Get recent payments/disputes (you'll add this with Stripe webhook data later)
  const { data: recentActivity } = await supabase
    .from('payments')
    .select(`
      id,
      writer_id,
      amount,
      status,
      payment_type,
      created_at,
      users!writer_id(name)
    `)
    .order('created_at', { ascending: false })
    .limit(20)

  return (
    <AdminAuthCheck>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Creator Administration</h1>
            <p className="text-gray-600 mt-2">Manage creators, payments, and platform compliance</p>
          </div>

          <AdminDashboard
            creators={creators || []}
            subscriptionStats={subscriptionStats || []}
            recentActivity={recentActivity || []}
          />
        </div>
      </div>
    </AdminAuthCheck>
  )
}
